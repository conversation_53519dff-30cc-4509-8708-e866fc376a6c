local xml2lua = require("lib.xml2lua.xml2lua")
local handler = require("lib.xml2lua.xmlhandler.tree")
local scene = require("client.ui.scene")

local manager = {}

local scenes = {}
local templates = {}

-- Helper function to parse color strings like "1, 1, 1, 1" into Color objects
local function parseColor(colorStr)
	if not colorStr then return Color.white end

	local r, g, b, a = colorStr:match("([%d%.]+),%s*([%d%.]+),%s*([%d%.]+),%s*([%d%.]+)")
	if r and g and b and a then
		return Color.new(tonumber(r) * 255, tonumber(g) * 255, tonumber(b) * 255, tonumber(a) * 255)
	end

	-- Fallback to white if parsing fails
	return Color.white
end

-- Helper function to parse position/size strings like "0,0" into Vector2 objects
local function parseVector2(vecStr)
	if not vecStr then return Vector2.new(0, 0) end

	local x, y = vecStr:match("([%d%.%-]+),%s*([%d%.%-]+)")
	if x and y then
		return Vector2.new(tonumber(x), tonumber(y))
	end

	return Vector2.new(0, 0)
end

-- Helper function to substitute template parameters
local function substituteParams(text, params)
	if not text or not params then return text end

	return text:gsub("{([^}]+)}", function(paramName)
		return params[paramName] or ("{" .. paramName .. "}")
	end)
end

-- Parse template parameters from XML
local function parseTemplateParams(paramsNode)
	local params = {}

	if paramsNode and paramsNode.Param then
		local paramList = paramsNode.Param
		-- Handle single param vs array of params
		if paramList.Name then
			paramList = {paramList}
		end

		for _, param in ipairs(paramList) do
			if param._attr then
				params[param._attr.Name] = param._attr.Default
			end
		end
	end

	return params
end

-- Create UI entity from XML node
local function createEntityFromXML(xmlNode, parentEntity, templateParams)
	if not xmlNode then return nil end

	local entityId = xmlNode._attr and xmlNode._attr.Id or "UnnamedEntity"
	entityId = substituteParams(entityId, templateParams)

	local entity = LS13.ECSManager.entity(entityId)

	-- Add UiElement component
	entity:give("UiElement", parentEntity)

	-- Process child nodes to add components
	for nodeName, nodeData in pairs(xmlNode) do
		if nodeName ~= "_attr" then
			local componentName = nodeName

			-- Handle component attributes
			if nodeData._attr then
				local attrs = nodeData._attr

				if componentName == "UiTransform" then
					local position = parseVector2(substituteParams(attrs.Position, templateParams))
					local size = parseVector2(substituteParams(attrs.Size, templateParams))
					local rotation = tonumber(substituteParams(attrs.Rotation or "0", templateParams)) or 0
					local posX = substituteParams(attrs.PosX or "pixel", templateParams)
					local posY = substituteParams(attrs.PosY or "pixel", templateParams)
					local sizeX = substituteParams(attrs.SizeX or "pixel", templateParams)
					local sizeY = substituteParams(attrs.SizeY or "pixel", templateParams)
					local anchor = parseVector2(substituteParams(attrs.Anchor or "0,0", templateParams))

					entity:give("UiTransform", position, size, rotation, posX, posY, sizeX, sizeY, anchor)

				elseif componentName == "UiLabel" then
					local text = substituteParams(attrs.Text or "", templateParams)
					local color = parseColor(substituteParams(attrs.Color, templateParams))
					local font = substituteParams(attrs.Font or "Font.Default", templateParams)
					local hAlign = substituteParams(attrs.HAlign or "left", templateParams)
					local vAlign = substituteParams(attrs.VAlign or "top", templateParams)

					entity:give("UiLabel", text, color, font, hAlign, vAlign)

				elseif componentName == "UiPanel" then
					local graphic = substituteParams(attrs.Graphic or "Graphic.UiPanel", templateParams)
					local color = parseColor(substituteParams(attrs.Color, templateParams))

					entity:give("UiPanel", graphic, color)

				elseif componentName == "UiLayout" then
					local layoutType = substituteParams(attrs.Type or "vertical", templateParams):lower()
					local padding = parseVector2(substituteParams(attrs.Padding or "0,0", templateParams))
					local spacing = tonumber(substituteParams(attrs.Spacing or "0", templateParams)) or 0
					local align = substituteParams(attrs.Align or "begin", templateParams)
					local justify = substituteParams(attrs.Justify or "begin", templateParams)

					entity:give("UiLayout", layoutType, padding, spacing, align, justify)

				elseif componentName == "UiTarget" then
					entity:give("UiTarget")
				end
			else
				-- Component without attributes
				if componentName == "UiTarget" then
					entity:give("UiTarget")
				end
			end
		end
	end

	return entity
end

-- Process template usage (like <UIButton>)
local function processTemplateUsage(templateName, attrs, parentEntity, world)
	local template = templates[templateName]
	if not template then
		LS13.Logging.LogWarning("Template not found: %s", templateName)
		return nil
	end

	-- Merge template defaults with provided attributes
	local params = {}
	for key, defaultValue in pairs(template.params) do
		params[key] = defaultValue
	end

	if attrs then
		for key, value in pairs(attrs) do
			params[key] = value
		end
	end

	-- Create entity from template
	local entity = createEntityFromXML(template.content, parentEntity, params)
	if entity and world then
		world:addEntity(entity)
	end

	return entity
end

function manager.parsePrototype(xmlString)
	local tree = handler:new()
	local parser = xml2lua.parser(tree)
	parser:parse(xmlString)

	if not tree.root or not tree.root.LS13 then
		LS13.Logging.LogError("Invalid UI prototype: missing LS13 root")
		return false
	end

	local ls13 = tree.root.LS13
	local markupList = ls13.UIMarkup

	-- Handle single markup vs array of markups
	if markupList and markupList._attr then
		markupList = {markupList}
	end

	if not markupList then
		LS13.Logging.LogError("No UIMarkup found in prototype")
		return false
	end

	for _, markup in ipairs(markupList) do
		if markup._attr then
			local id = markup._attr.Id
			local markupType = markup._attr.Type

			if markupType == "Template" then
				-- Parse template
				local templateParams = parseTemplateParams(markup.Params)
				local content = markup.UIEntity

				templates[id] = {
					params = templateParams,
					content = content
				}

				LS13.Logging.LogDebug("Registered template: %s", id)

			elseif markupType == "Scene" then
				-- Store scene definition
				scenes[id] = markup
				LS13.Logging.LogDebug("Registered scene: %s", id)
			end
		end
	end

	return true
end

-- Load prototype file
function manager.loadPrototype(path)
	local info = love.filesystem.getInfo(path, "file")
	if not info or info.type ~= "file" then
		LS13.Logging.LogError("Prototype file not found: %s", path)
		return false
	end

	local xmlString = love.filesystem.read(path)
	if not xmlString then
		LS13.Logging.LogError("Failed to read prototype file: %s", path)
		return false
	end

	return manager.parsePrototype(xmlString)
end

-- Create a scene from a scene ID
function manager.createScene(sceneId, world)
	local sceneData = scenes[sceneId]
	if not sceneData then
		LS13.Logging.LogError("Scene not found: %s", sceneId)
		return nil
	end

	local sceneInstance = scene.new()
	sceneInstance.id = sceneId
	sceneInstance.entities = {}

	-- Process the scene's UI elements
	local rootElement = sceneData.UIElement
	if rootElement then
		local rootEntity = manager.createUIElement(rootElement, nil, world, sceneInstance)
		if rootEntity then
			sceneInstance.rootEntity = rootEntity
		end
	end

	return sceneInstance
end

-- Create UI element from XML node (handles both regular elements and template usage)
function manager.createUIElement(xmlNode, parentEntity, world, sceneInstance)
	if not xmlNode then return nil end

	local entities = {}

	-- First, create the root entity if this node has attributes or is the root
	local rootEntity = nil
	if xmlNode._attr or not parentEntity then
		rootEntity = createEntityFromXML(xmlNode, parentEntity, {})
		if rootEntity and world then
			world:addEntity(rootEntity)
			if sceneInstance then
				table.insert(sceneInstance.entities, rootEntity)
				if xmlNode._attr and xmlNode._attr.Id then
					sceneInstance.entities[xmlNode._attr.Id] = rootEntity
				end
			end
		end
		table.insert(entities, rootEntity)
	end

	-- Use the root entity as parent for children, or the provided parent
	local childParent = rootEntity or parentEntity

	-- Process all child nodes
	for nodeName, nodeData in pairs(xmlNode) do
		if nodeName ~= "_attr" then
			local entity = nil

			-- Check if this is a template usage (like UIButton)
			if templates[nodeName] then
				entity = processTemplateUsage(nodeName, nodeData._attr, childParent, world)
				if entity and sceneInstance then
					table.insert(sceneInstance.entities, entity)
					if nodeData._attr and nodeData._attr.Id then
						sceneInstance.entities[nodeData._attr.Id] = entity
					end
				end
			elseif nodeName == "UIElement" then
				-- Recursive UIElement
				local childEntities = manager.createUIElement(nodeData, childParent, world, sceneInstance)
				if childEntities then
					for _, childEntity in ipairs(childEntities) do
						table.insert(entities, childEntity)
					end
				end
			else
				-- Regular component, skip (handled in createEntityFromXML)
			end

			if entity then
				table.insert(entities, entity)
			end
		end
	end

	return entities
end

-- Initialize the manager by loading core prototypes
function manager.init()
	-- Load core UI templates
	manager.loadPrototype("resources/prototypes/ui/_core.xml")

	LS13.Logging.LogInfo("UI Manager initialized")
end

-- Get a registered template
function manager.getTemplate(templateId)
	return templates[templateId]
end

-- Get a registered scene
function manager.getScene(sceneId)
	return scenes[sceneId]
end

-- List all registered templates
function manager.listTemplates()
	local templateList = {}
	for id, _ in pairs(templates) do
		table.insert(templateList, id)
	end
	return templateList
end

-- List all registered scenes
function manager.listScenes()
	local sceneList = {}
	for id, _ in pairs(scenes) do
		table.insert(sceneList, id)
	end
	return sceneList
end

return manager
